﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ProductDetection.Core.Models;
using ProductDetection.Core.Services;

namespace ProductDetection.Training;

class Program
{
    static async Task Main(string[] args)
    {
        // Setup configuration
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: true)
            .AddCommandLine(args)
            .Build();

        // Setup dependency injection
        var services = new ServiceCollection();
        ConfigureServices(services, configuration);
        var serviceProvider = services.BuildServiceProvider();

        // Get logger
        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();

        try
        {
            logger.LogInformation("Starting Product Detection Training Application");

            // Parse command line arguments
            var config = ParseArguments(args, configuration, logger);
            if (config == null)
            {
                ShowUsage();
                return;
            }

            // Get services
            var dataLoader = serviceProvider.GetRequiredService<ProductDataLoader>();
            var trainingService = serviceProvider.GetRequiredService<ProductTrainingService>();
            var modelPersistence = serviceProvider.GetRequiredService<ModelPersistenceService>();

            // Execute training
            await ExecuteTrainingAsync(dataLoader, trainingService, modelPersistence, config, logger);

            logger.LogInformation("Training completed successfully!");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Training failed with error");
            Environment.Exit(1);
        }
    }

    static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Information);
        });

        services.AddSingleton<ProductDataLoader>();
        services.AddSingleton<ProductTrainingService>();
        services.AddSingleton<ModelPersistenceService>();
    }

    static TrainingConfig? ParseArguments(string[] args, IConfiguration configuration, ILogger logger)
    {
        var config = new TrainingConfig();

        // Load from configuration
        configuration.GetSection("Training").Bind(config);

        // Override with command line arguments
        for (int i = 0; i < args.Length; i++)
        {
            switch (args[i].ToLower())
            {
                case "--products-path":
                case "-p":
                    if (i + 1 < args.Length)
                        config.ProductsPath = args[++i];
                    break;
                case "--models-path":
                case "-m":
                    if (i + 1 < args.Length)
                        config.ModelsPath = args[++i];
                    break;
                case "--model-name":
                case "-n":
                    if (i + 1 < args.Length)
                        config.ModelName = args[++i];
                    break;
                case "--epochs":
                case "-e":
                    if (i + 1 < args.Length && int.TryParse(args[++i], out var epochs))
                        config.Epochs = epochs;
                    break;
                case "--batch-size":
                case "-b":
                    if (i + 1 < args.Length && int.TryParse(args[++i], out var batchSize))
                        config.BatchSize = batchSize;
                    break;
                case "--learning-rate":
                case "-lr":
                    if (i + 1 < args.Length && float.TryParse(args[++i], out var learningRate))
                        config.LearningRate = learningRate;
                    break;
                case "--validation-split":
                case "-vs":
                    if (i + 1 < args.Length && float.TryParse(args[++i], out var validationSplit))
                        config.ValidationSplit = validationSplit;
                    break;
                case "--help":
                case "-h":
                    return null;
            }
        }

        // Validate configuration
        if (!Directory.Exists(config.ProductsPath))
        {
            logger.LogError("Products directory not found: {ProductsPath}", config.ProductsPath);
            return null;
        }

        logger.LogInformation("Training Configuration:");
        logger.LogInformation("Products Path: {ProductsPath}", config.ProductsPath);
        logger.LogInformation("Models Path: {ModelsPath}", config.ModelsPath);
        logger.LogInformation("Model Name: {ModelName}", config.ModelName);
        logger.LogInformation("Epochs: {Epochs}", config.Epochs);
        logger.LogInformation("Batch Size: {BatchSize}", config.BatchSize);
        logger.LogInformation("Learning Rate: {LearningRate}", config.LearningRate);
        logger.LogInformation("Validation Split: {ValidationSplit}", config.ValidationSplit);

        return config;
    }

    static void ShowUsage()
    {
        Console.WriteLine("Product Detection Training Application");
        Console.WriteLine();
        Console.WriteLine("Usage: ProductDetection.Training [options]");
        Console.WriteLine();
        Console.WriteLine("Options:");
        Console.WriteLine("  -p, --products-path <path>     Path to products directory (default: products)");
        Console.WriteLine("  -m, --models-path <path>       Path to models directory (default: models)");
        Console.WriteLine("  -n, --model-name <name>        Name of the model (default: product-detection-model)");
        Console.WriteLine("  -e, --epochs <number>          Number of training epochs (default: 100)");
        Console.WriteLine("  -b, --batch-size <number>      Batch size for training (default: 32)");
        Console.WriteLine("  -lr, --learning-rate <rate>    Learning rate (default: 0.001)");
        Console.WriteLine("  -vs, --validation-split <rate> Validation split ratio (default: 0.2)");
        Console.WriteLine("  -h, --help                     Show this help message");
        Console.WriteLine();
        Console.WriteLine("Example:");
        Console.WriteLine("  ProductDetection.Training -p ./products -m ./models -n my-model -e 50");
    }

    static async Task ExecuteTrainingAsync(
        ProductDataLoader dataLoader,
        ProductTrainingService trainingService,
        ModelPersistenceService modelPersistence,
        TrainingConfig config,
        ILogger logger)
    {
        logger.LogInformation("Starting training process...");

        // Load product data
        logger.LogInformation("Loading product data from: {ProductsPath}", config.ProductsPath);
        var allData = await dataLoader.LoadProductDataAsync(config.ProductsPath);

        if (allData.Count == 0)
        {
            throw new InvalidOperationException("No training data found. Please check your products directory structure.");
        }

        // Get unique labels
        var labels = dataLoader.GetUniqueLabels(allData);
        logger.LogInformation("Found {LabelCount} product categories: {Labels}",
            labels.Count, string.Join(", ", labels));

        if (labels.Count < 2)
        {
            throw new InvalidOperationException("At least 2 product categories are required for training.");
        }

        // Split data
        var (trainingData, validationData) = dataLoader.SplitData(allData, config.ValidationSplit);

        // Train model
        logger.LogInformation("Training model...");
        var model = await trainingService.TrainProductClassificationModelAsync(
            trainingData, validationData, config);

        // Evaluate model
        logger.LogInformation("Evaluating model...");
        var metrics = trainingService.EvaluateModel(model, validationData);

        // Save model
        var modelPath = Path.Combine(config.ModelsPath, $"{config.ModelName}.zip");
        logger.LogInformation("Saving model to: {ModelPath}", modelPath);

        // Create a sample data view for schema
        var mlContext = new Microsoft.ML.MLContext();
        var sampleDataView = mlContext.Data.LoadFromEnumerable(trainingData.Take(1));
        var schema = sampleDataView.Schema;

        await modelPersistence.SaveModelAsync(model, schema, modelPath, labels, config);

        logger.LogInformation("Training completed successfully!");
        logger.LogInformation("Model saved to: {ModelPath}", modelPath);
        logger.LogInformation("Final Accuracy: {Accuracy:F4}", metrics.MacroAccuracy);
    }
}
