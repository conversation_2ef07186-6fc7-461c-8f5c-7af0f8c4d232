using Microsoft.Extensions.Logging;
using Microsoft.ML;
using Microsoft.ML.Data;
using ProductDetection.Core.Models;
using System.Text.Json;

namespace ProductDetection.Core.Services;

/// <summary>
/// Service for saving and loading trained models
/// </summary>
public class ModelPersistenceService
{
    private readonly ILogger<ModelPersistenceService> _logger;
    private readonly MLContext _mlContext;

    public ModelPersistenceService(ILogger<ModelPersistenceService> logger)
    {
        _logger = logger;
        _mlContext = new MLContext();
    }

    /// <summary>
    /// Save a trained model to disk
    /// </summary>
    public async Task SaveModelAsync(
        ITransformer model, 
        DataViewSchema schema,
        string modelPath, 
        List<string> labels,
        TrainingConfig config)
    {
        try
        {
            // Ensure model directory exists
            var modelDir = Path.GetDirectoryName(modelPath);
            if (!string.IsNullOrEmpty(modelDir) && !Directory.Exists(modelDir))
            {
                Directory.CreateDirectory(modelDir);
            }

            // Save the ML.NET model
            _mlContext.Model.Save(model, schema, modelPath);
            _logger.LogInformation("Model saved to: {ModelPath}", modelPath);

            // Save metadata
            var metadata = new ModelMetadata
            {
                ModelPath = modelPath,
                Labels = labels,
                TrainingConfig = config,
                CreatedAt = DateTime.UtcNow,
                Version = "1.0"
            };

            var metadataPath = Path.ChangeExtension(modelPath, ".metadata.json");
            var metadataJson = JsonSerializer.Serialize(metadata, new JsonSerializerOptions 
            { 
                WriteIndented = true 
            });
            
            await File.WriteAllTextAsync(metadataPath, metadataJson);
            _logger.LogInformation("Model metadata saved to: {MetadataPath}", metadataPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save model to {ModelPath}", modelPath);
            throw;
        }
    }

    /// <summary>
    /// Load a trained model from disk
    /// </summary>
    public async Task<(ITransformer model, ModelMetadata metadata)> LoadModelAsync(string modelPath)
    {
        try
        {
            if (!File.Exists(modelPath))
            {
                throw new FileNotFoundException($"Model file not found: {modelPath}");
            }

            // Load the ML.NET model
            var model = _mlContext.Model.Load(modelPath, out var schema);
            _logger.LogInformation("Model loaded from: {ModelPath}", modelPath);

            // Load metadata
            var metadataPath = Path.ChangeExtension(modelPath, ".metadata.json");
            ModelMetadata metadata;

            if (File.Exists(metadataPath))
            {
                var metadataJson = await File.ReadAllTextAsync(metadataPath);
                metadata = JsonSerializer.Deserialize<ModelMetadata>(metadataJson) 
                    ?? throw new InvalidOperationException("Failed to deserialize model metadata");
                _logger.LogInformation("Model metadata loaded from: {MetadataPath}", metadataPath);
            }
            else
            {
                _logger.LogWarning("Model metadata not found, using default values");
                metadata = new ModelMetadata
                {
                    ModelPath = modelPath,
                    Labels = new List<string>(),
                    CreatedAt = DateTime.UtcNow,
                    Version = "Unknown"
                };
            }

            return (model, metadata);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load model from {ModelPath}", modelPath);
            throw;
        }
    }

    /// <summary>
    /// Validate that a model file exists and is accessible
    /// </summary>
    public bool ValidateModelExists(string modelPath)
    {
        var exists = File.Exists(modelPath);
        if (!exists)
        {
            _logger.LogWarning("Model file not found: {ModelPath}", modelPath);
        }
        return exists;
    }

    /// <summary>
    /// Get model information without fully loading it
    /// </summary>
    public async Task<ModelMetadata?> GetModelMetadataAsync(string modelPath)
    {
        try
        {
            var metadataPath = Path.ChangeExtension(modelPath, ".metadata.json");
            
            if (!File.Exists(metadataPath))
            {
                _logger.LogWarning("Model metadata file not found: {MetadataPath}", metadataPath);
                return null;
            }

            var metadataJson = await File.ReadAllTextAsync(metadataPath);
            var metadata = JsonSerializer.Deserialize<ModelMetadata>(metadataJson);
            
            return metadata;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load model metadata from {ModelPath}", modelPath);
            return null;
        }
    }

    /// <summary>
    /// List all available models in the models directory
    /// </summary>
    public async Task<List<ModelInfo>> ListAvailableModelsAsync(string modelsDirectory)
    {
        var models = new List<ModelInfo>();

        if (!Directory.Exists(modelsDirectory))
        {
            _logger.LogWarning("Models directory not found: {ModelsDirectory}", modelsDirectory);
            return models;
        }

        var modelFiles = Directory.GetFiles(modelsDirectory, "*.zip", SearchOption.AllDirectories);

        foreach (var modelFile in modelFiles)
        {
            try
            {
                var metadata = await GetModelMetadataAsync(modelFile);
                var fileInfo = new FileInfo(modelFile);

                models.Add(new ModelInfo
                {
                    ModelPath = modelFile,
                    FileName = fileInfo.Name,
                    FileSize = fileInfo.Length,
                    CreatedAt = metadata?.CreatedAt ?? fileInfo.CreationTimeUtc,
                    Version = metadata?.Version ?? "Unknown",
                    LabelCount = metadata?.Labels?.Count ?? 0
                });
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to process model file: {ModelFile}", modelFile);
            }
        }

        _logger.LogInformation("Found {Count} available models", models.Count);
        return models;
    }
}

/// <summary>
/// Model metadata for persistence
/// </summary>
public class ModelMetadata
{
    public string ModelPath { get; set; } = string.Empty;
    public List<string> Labels { get; set; } = new();
    public TrainingConfig? TrainingConfig { get; set; }
    public DateTime CreatedAt { get; set; }
    public string Version { get; set; } = string.Empty;
}

/// <summary>
/// Model information for listing
/// </summary>
public class ModelInfo
{
    public string ModelPath { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public DateTime CreatedAt { get; set; }
    public string Version { get; set; } = string.Empty;
    public int LabelCount { get; set; }
}
