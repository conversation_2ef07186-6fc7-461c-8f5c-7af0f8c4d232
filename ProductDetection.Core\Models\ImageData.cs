using Microsoft.ML.Data;

namespace ProductDetection.Core.Models;

/// <summary>
/// Input data model for image processing
/// </summary>
public class ImageData
{
    [LoadColumn(0)]
    public string? ImagePath { get; set; }

    [LoadColumn(1)]
    public string? Label { get; set; }
}

/// <summary>
/// Model for image with pixel data
/// </summary>
public class ImageInputData
{
    public MLImage? Image { get; set; }

    public string? ImagePath { get; set; }
    public string? Label { get; set; }
}

/// <summary>
/// Bounding box coordinates for object detection
/// </summary>
public class BoundingBox
{
    public float X { get; set; }
    public float Y { get; set; }
    public float Width { get; set; }
    public float Height { get; set; }
    public float Confidence { get; set; }
    public string? Label { get; set; }
}

/// <summary>
/// YOLO detection output
/// </summary>
public class YoloOutput
{
    [VectorType(1000)]
    public float[]? PredictedLabels { get; set; }

    [VectorType(4)]
    public float[]? BoundingBoxes { get; set; }

    [VectorType(1)]
    public float[]? Confidence { get; set; }
}

/// <summary>
/// Product detection result
/// </summary>
public class ProductDetectionResult
{
    public string? ProductName { get; set; }
    public float Confidence { get; set; }
    public BoundingBox? BoundingBox { get; set; }
    public string? ImagePath { get; set; }
}

/// <summary>
/// Training configuration
/// </summary>
public class TrainingConfig
{
    public string ProductsPath { get; set; } = "products";
    public string ModelsPath { get; set; } = "models";
    public string ModelName { get; set; } = "product-detection-model";
    public int ImageSize { get; set; } = 224;
    public int Epochs { get; set; } = 100;
    public float LearningRate { get; set; } = 0.001f;
    public int BatchSize { get; set; } = 32;
    public float ValidationSplit { get; set; } = 0.2f;
}
