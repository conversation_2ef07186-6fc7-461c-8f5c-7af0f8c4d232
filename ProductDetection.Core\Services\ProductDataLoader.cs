using Microsoft.Extensions.Logging;
using ProductDetection.Core.Models;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Formats.Jpeg;
using SixLabors.ImageSharp.Processing;

namespace ProductDetection.Core.Services;

/// <summary>
/// Service for loading and processing product images from folder structure
/// </summary>
public class ProductDataLoader
{
    private readonly ILogger<ProductDataLoader> _logger;
    private readonly string[] _supportedExtensions = { ".jpg", ".jpeg", ".png", ".bmp", ".gif" };

    public ProductDataLoader(ILogger<ProductDataLoader> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Load product images from folder structure
    /// Expected structure: products/product1/*.jpg, products/product2/*.jpg, etc.
    /// </summary>
    public async Task<List<ProductTrainingData>> LoadProductDataAsync(string productsPath)
    {
        var trainingData = new List<ProductTrainingData>();

        if (!Directory.Exists(productsPath))
        {
            _logger.LogError("Products directory not found: {ProductsPath}", productsPath);
            throw new DirectoryNotFoundException($"Products directory not found: {productsPath}");
        }

        var productDirectories = Directory.GetDirectories(productsPath);
        _logger.LogInformation("Found {Count} product directories", productDirectories.Length);

        foreach (var productDir in productDirectories)
        {
            var productName = Path.GetFileName(productDir);
            _logger.LogInformation("Processing product: {ProductName}", productName);

            var imageFiles = Directory.GetFiles(productDir)
                .Where(file => _supportedExtensions.Contains(Path.GetExtension(file).ToLowerInvariant()))
                .ToArray();

            _logger.LogInformation("Found {Count} images for product {ProductName}", imageFiles.Length, productName);

            foreach (var imageFile in imageFiles)
            {
                try
                {
                    // Validate image can be loaded
                    await ValidateImageAsync(imageFile);

                    trainingData.Add(new ProductTrainingData
                    {
                        ImagePath = imageFile,
                        Label = productName
                    });
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to process image: {ImageFile}", imageFile);
                }
            }
        }

        _logger.LogInformation("Loaded {Count} training images total", trainingData.Count);
        return trainingData;
    }

    /// <summary>
    /// Validate that an image can be loaded and processed
    /// </summary>
    private async Task ValidateImageAsync(string imagePath)
    {
        using var image = await Image.LoadAsync(imagePath);
        if (image.Width == 0 || image.Height == 0)
        {
            throw new InvalidOperationException($"Invalid image dimensions: {imagePath}");
        }
    }

    /// <summary>
    /// Preprocess images for training (resize, normalize, etc.)
    /// </summary>
    public async Task<string> PreprocessImageAsync(string inputPath, string outputPath, int targetSize = 224)
    {
        using var image = await Image.LoadAsync(inputPath);
        
        // Resize image while maintaining aspect ratio
        image.Mutate(x => x.Resize(new ResizeOptions
        {
            Size = new Size(targetSize, targetSize),
            Mode = ResizeMode.Crop,
            Position = AnchorPositionMode.Center
        }));

        // Ensure output directory exists
        var outputDir = Path.GetDirectoryName(outputPath);
        if (!string.IsNullOrEmpty(outputDir) && !Directory.Exists(outputDir))
        {
            Directory.CreateDirectory(outputDir);
        }

        await image.SaveAsync(outputPath, new JpegEncoder { Quality = 90 });
        return outputPath;
    }

    /// <summary>
    /// Split data into training and validation sets
    /// </summary>
    public (List<ProductTrainingData> training, List<ProductTrainingData> validation) 
        SplitData(List<ProductTrainingData> data, float validationSplit = 0.2f)
    {
        var random = new Random(42); // Fixed seed for reproducibility
        var shuffled = data.OrderBy(x => random.Next()).ToList();
        
        var validationCount = (int)(data.Count * validationSplit);
        var validation = shuffled.Take(validationCount).ToList();
        var training = shuffled.Skip(validationCount).ToList();

        _logger.LogInformation("Split data: {TrainingCount} training, {ValidationCount} validation", 
            training.Count, validation.Count);

        return (training, validation);
    }

    /// <summary>
    /// Get unique product labels from the dataset
    /// </summary>
    public List<string> GetUniqueLabels(List<ProductTrainingData> data)
    {
        var labels = data.Select(x => x.Label)
            .Where(x => !string.IsNullOrEmpty(x))
            .Distinct()
            .OrderBy(x => x)
            .ToList();

        _logger.LogInformation("Found {Count} unique product labels: {Labels}", 
            labels.Count, string.Join(", ", labels));

        return labels!;
    }

    /// <summary>
    /// Create a mapping of labels to indices
    /// </summary>
    public Dictionary<string, int> CreateLabelMapping(List<string> labels)
    {
        var mapping = labels.Select((label, index) => new { label, index })
            .ToDictionary(x => x.label, x => x.index);

        _logger.LogInformation("Created label mapping for {Count} labels", mapping.Count);
        return mapping;
    }
}
