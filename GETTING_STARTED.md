# Getting Started with Product Detection

This guide will help you quickly set up and start using the Product Detection system.

## Quick Start

### 1. Verify Installation
```bash
dotnet --version  # Should show 9.0.x
```

### 2. Build the Solution
```bash
dotnet build
```

### 3. Prepare Your Data
Create product folders in the `products` directory:
```
products/
├── apple/       # Add your apple images here
├── banana/      # Add your banana images here
└── orange/      # Add your orange images here
```

### 4. Train Your Model
```bash
# Basic training (uses default settings)
dotnet run --project ProductDetection.Training

# Custom training
dotnet run --project ProductDetection.Training -- --epochs 50 --batch-size 16
```

### 5. Test Your Model
```bash
# Test a single image
dotnet run --project ProductDetection.Testing -- -m models/product-detection-model.zip -i test-image.jpg

# Interactive mode
dotnet run --project ProductDetection.Testing -- -m models/product-detection-model.zip --interactive
```

## What You Get

✅ **Secure Code Protection**: Your ML logic is compiled and protected  
✅ **Easy Training**: Just organize images in folders  
✅ **Fast Inference**: Optimized for real-time detection  
✅ **Flexible Deployment**: Self-contained executables  
✅ **Professional Logging**: Detailed training and inference logs  

## Next Steps

1. **Add Your Products**: Replace sample folders with your actual product images
2. **Optimize Training**: Adjust epochs, batch size, and learning rate
3. **Test Thoroughly**: Use the interactive mode to test various images
4. **Deploy**: Build release versions for production use

## Need Help?

- Check the main README.md for detailed documentation
- Review the logs for training progress and errors
- Ensure you have enough training images (10+ per category)
- Verify image formats are supported (.jpg, .png, .bmp, .gif)

## Production Deployment

For production use:
```bash
# Build optimized release versions
dotnet publish ProductDetection.Training -c Release --self-contained
dotnet publish ProductDetection.Testing -c Release --self-contained
```

This creates standalone executables that don't require .NET runtime on target machines.
