using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ProductDetection.Core.Models;

namespace ProductDetection.Core.Services;

/// <summary>
/// Service for managing application configuration
/// </summary>
public class ConfigurationService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<ConfigurationService> _logger;

    public ConfigurationService(IConfiguration configuration, ILogger<ConfigurationService> logger)
    {
        _configuration = configuration;
        _logger = logger;
    }

    /// <summary>
    /// Get training configuration from settings
    /// </summary>
    public TrainingConfig GetTrainingConfig()
    {
        var config = new TrainingConfig();
        _configuration.GetSection("Training").Bind(config);
        
        _logger.LogInformation("Loaded training configuration: {Config}", 
            System.Text.Json.JsonSerializer.Serialize(config));
        
        return config;
    }

    /// <summary>
    /// Get testing configuration from settings
    /// </summary>
    public TestingConfig GetTestingConfig()
    {
        var config = new TestingConfig();
        _configuration.GetSection("Testing").Bind(config);
        
        _logger.LogInformation("Loaded testing configuration: {Config}", 
            System.Text.Json.JsonSerializer.Serialize(config));
        
        return config;
    }

    /// <summary>
    /// Validate that required directories exist
    /// </summary>
    public bool ValidateDirectories(TrainingConfig config)
    {
        var isValid = true;

        if (!Directory.Exists(config.ProductsPath))
        {
            _logger.LogError("Products directory not found: {ProductsPath}", config.ProductsPath);
            isValid = false;
        }

        if (!Directory.Exists(config.ModelsPath))
        {
            _logger.LogInformation("Creating models directory: {ModelsPath}", config.ModelsPath);
            try
            {
                Directory.CreateDirectory(config.ModelsPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create models directory: {ModelsPath}", config.ModelsPath);
                isValid = false;
            }
        }

        return isValid;
    }

    /// <summary>
    /// Get application settings value
    /// </summary>
    public T? GetValue<T>(string key)
    {
        return _configuration.GetValue<T>(key);
    }

    /// <summary>
    /// Get connection string
    /// </summary>
    public string? GetConnectionString(string name)
    {
        return _configuration.GetConnectionString(name);
    }
}

/// <summary>
/// Testing configuration
/// </summary>
public class TestingConfig
{
    public string ModelPath { get; set; } = "models/product-detection-model.zip";
    public bool EnableDetailedLogging { get; set; } = true;
    public float ConfidenceThreshold { get; set; } = 0.5f;
}
