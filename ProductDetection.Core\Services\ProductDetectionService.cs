using Microsoft.Extensions.Logging;
using Microsoft.ML;
using Microsoft.ML.Data;
using ProductDetection.Core.Models;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;

namespace ProductDetection.Core.Services;

/// <summary>
/// Service for detecting products in images using trained models
/// </summary>
public class ProductDetectionService
{
    private readonly ILogger<ProductDetectionService> _logger;
    private readonly ModelPersistenceService _modelPersistence;
    private readonly MLContext _mlContext;
    
    private ITransformer? _model;
    private ModelMetadata? _modelMetadata;
    private PredictionEngine<ProductClassificationInput, ProductClassificationOutput>? _predictionEngine;

    public ProductDetectionService(
        ILogger<ProductDetectionService> logger,
        ModelPersistenceService modelPersistence)
    {
        _logger = logger;
        _modelPersistence = modelPersistence;
        _mlContext = new MLContext();
    }

    /// <summary>
    /// Load a trained model for inference
    /// </summary>
    public async Task LoadModelAsync(string modelPath)
    {
        try
        {
            _logger.LogInformation("Loading model from: {ModelPath}", modelPath);

            var (model, metadata) = await _modelPersistence.LoadModelAsync(modelPath);
            _model = model;
            _modelMetadata = metadata;

            // Create prediction engine
            _predictionEngine = _mlContext.Model.CreatePredictionEngine<ProductClassificationInput, ProductClassificationOutput>(_model);

            _logger.LogInformation("Model loaded successfully. Labels: {Labels}", 
                string.Join(", ", _modelMetadata.Labels));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load model from {ModelPath}", modelPath);
            throw;
        }
    }

    /// <summary>
    /// Detect product in an image
    /// </summary>
    public async Task<ProductDetectionResult> DetectProductAsync(string imagePath)
    {
        if (_predictionEngine == null || _modelMetadata == null)
        {
            throw new InvalidOperationException("Model not loaded. Call LoadModelAsync first.");
        }

        if (!File.Exists(imagePath))
        {
            throw new FileNotFoundException($"Image file not found: {imagePath}");
        }

        try
        {
            _logger.LogInformation("Detecting product in image: {ImagePath}", imagePath);

            // Load and preprocess image
            var processedImagePath = await PreprocessImageForInferenceAsync(imagePath);
            
            // Create input
            var input = new ProductClassificationInput();
            
            // Load image as MLImage
            input.Image = MLImage.CreateFromFile(processedImagePath);

            // Make prediction
            var prediction = _predictionEngine.Predict(input);

            // Find the best prediction
            var bestPrediction = GetBestPrediction(prediction);

            var result = new ProductDetectionResult
            {
                ProductName = bestPrediction.label,
                Confidence = bestPrediction.confidence,
                ImagePath = imagePath,
                BoundingBox = new BoundingBox
                {
                    X = 0,
                    Y = 0,
                    Width = 1.0f,
                    Height = 1.0f,
                    Confidence = bestPrediction.confidence,
                    Label = bestPrediction.label
                }
            };

            _logger.LogInformation("Detection result: {ProductName} (Confidence: {Confidence:F4})", 
                result.ProductName, result.Confidence);

            // Clean up temporary file
            if (processedImagePath != imagePath && File.Exists(processedImagePath))
            {
                File.Delete(processedImagePath);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to detect product in image: {ImagePath}", imagePath);
            throw;
        }
    }

    /// <summary>
    /// Detect products in multiple images
    /// </summary>
    public async Task<List<ProductDetectionResult>> DetectProductsAsync(IEnumerable<string> imagePaths)
    {
        var results = new List<ProductDetectionResult>();

        foreach (var imagePath in imagePaths)
        {
            try
            {
                var result = await DetectProductAsync(imagePath);
                results.Add(result);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to process image: {ImagePath}", imagePath);
                
                // Add failed result
                results.Add(new ProductDetectionResult
                {
                    ProductName = "Error",
                    Confidence = 0.0f,
                    ImagePath = imagePath
                });
            }
        }

        return results;
    }

    /// <summary>
    /// Get available product labels from the loaded model
    /// </summary>
    public List<string> GetAvailableLabels()
    {
        if (_modelMetadata == null)
        {
            throw new InvalidOperationException("Model not loaded. Call LoadModelAsync first.");
        }

        return _modelMetadata.Labels.ToList();
    }

    /// <summary>
    /// Check if a model is currently loaded
    /// </summary>
    public bool IsModelLoaded => _model != null && _predictionEngine != null;

    /// <summary>
    /// Get model information
    /// </summary>
    public ModelMetadata? GetModelMetadata() => _modelMetadata;

    /// <summary>
    /// Preprocess image for inference
    /// </summary>
    private async Task<string> PreprocessImageForInferenceAsync(string imagePath)
    {
        var tempPath = Path.GetTempFileName() + ".jpg";

        using var image = await Image.LoadAsync(imagePath);
        
        // Resize to model input size
        image.Mutate(x => x.Resize(new ResizeOptions
        {
            Size = new Size(224, 224),
            Mode = ResizeMode.Crop,
            Position = AnchorPositionMode.Center
        }));

        await image.SaveAsJpegAsync(tempPath);
        return tempPath;
    }

    /// <summary>
    /// Get the best prediction from the model output
    /// </summary>
    private (string label, float confidence) GetBestPrediction(ProductClassificationOutput prediction)
    {
        if (prediction.Score == null || _modelMetadata?.Labels == null)
        {
            return ("Unknown", 0.0f);
        }

        var maxIndex = 0;
        var maxScore = prediction.Score[0];

        for (int i = 1; i < prediction.Score.Length && i < _modelMetadata.Labels.Count; i++)
        {
            if (prediction.Score[i] > maxScore)
            {
                maxScore = prediction.Score[i];
                maxIndex = i;
            }
        }

        var label = maxIndex < _modelMetadata.Labels.Count 
            ? _modelMetadata.Labels[maxIndex] 
            : "Unknown";

        return (label, maxScore);
    }

    /// <summary>
    /// Dispose resources
    /// </summary>
    public void Dispose()
    {
        _predictionEngine?.Dispose();
    }
}
