﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ProductDetection.Core.Services;

namespace ProductDetection.Testing;

class Program
{
    static async Task Main(string[] args)
    {
        // Setup configuration
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: true)
            .AddCommandLine(args)
            .Build();

        // Setup dependency injection
        var services = new ServiceCollection();
        ConfigureServices(services, configuration);
        var serviceProvider = services.BuildServiceProvider();

        // Get logger
        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();

        try
        {
            logger.LogInformation("Starting Product Detection Testing Application");

            // Parse command line arguments
            var (modelPath, imagePaths, interactive) = ParseArguments(args, configuration, logger);

            if (string.IsNullOrEmpty(modelPath))
            {
                ShowUsage();
                return;
            }

            // Get services
            var detectionService = serviceProvider.GetRequiredService<ProductDetectionService>();

            // Load model
            logger.LogInformation("Loading model from: {ModelPath}", modelPath);
            await detectionService.LoadModelAsync(modelPath);

            if (interactive)
            {
                await RunInteractiveMode(detectionService, logger);
            }
            else if (imagePaths.Any())
            {
                await ProcessImages(detectionService, imagePaths, logger);
            }
            else
            {
                logger.LogError("No images specified. Use --image or --interactive mode.");
                ShowUsage();
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Application failed with error");
            Environment.Exit(1);
        }
    }

    static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Information);
        });

        services.AddSingleton<ModelPersistenceService>();
        services.AddSingleton<ProductDetectionService>();
    }

    static (string modelPath, List<string> imagePaths, bool interactive) ParseArguments(
        string[] args, IConfiguration configuration, ILogger logger)
    {
        var modelPath = configuration["Testing:ModelPath"] ?? "";
        var imagePaths = new List<string>();
        var interactive = false;

        for (int i = 0; i < args.Length; i++)
        {
            switch (args[i].ToLower())
            {
                case "--model":
                case "-m":
                    if (i + 1 < args.Length)
                        modelPath = args[++i];
                    break;
                case "--image":
                case "-i":
                    if (i + 1 < args.Length)
                        imagePaths.Add(args[++i]);
                    break;
                case "--interactive":
                case "-int":
                    interactive = true;
                    break;
                case "--help":
                case "-h":
                    return ("", new List<string>(), false);
            }
        }

        // Validate model path
        if (!string.IsNullOrEmpty(modelPath) && !File.Exists(modelPath))
        {
            logger.LogError("Model file not found: {ModelPath}", modelPath);
            return ("", new List<string>(), false);
        }

        // Validate image paths
        var validImagePaths = imagePaths.Where(File.Exists).ToList();
        if (imagePaths.Count != validImagePaths.Count)
        {
            var invalidPaths = imagePaths.Except(validImagePaths);
            logger.LogWarning("Some image files not found: {InvalidPaths}", string.Join(", ", invalidPaths));
        }

        return (modelPath, validImagePaths, interactive);
    }

    static void ShowUsage()
    {
        Console.WriteLine("Product Detection Testing Application");
        Console.WriteLine();
        Console.WriteLine("Usage: ProductDetection.Testing [options]");
        Console.WriteLine();
        Console.WriteLine("Options:");
        Console.WriteLine("  -m, --model <path>        Path to the trained model file (.zip)");
        Console.WriteLine("  -i, --image <path>        Path to image file to analyze (can be used multiple times)");
        Console.WriteLine("  -int, --interactive       Run in interactive mode");
        Console.WriteLine("  -h, --help               Show this help message");
        Console.WriteLine();
        Console.WriteLine("Examples:");
        Console.WriteLine("  ProductDetection.Testing -m ./models/my-model.zip -i ./test-image.jpg");
        Console.WriteLine("  ProductDetection.Testing -m ./models/my-model.zip --interactive");
        Console.WriteLine("  ProductDetection.Testing -m ./models/my-model.zip -i image1.jpg -i image2.jpg");
    }

    static async Task ProcessImages(ProductDetectionService detectionService, List<string> imagePaths, ILogger logger)
    {
        logger.LogInformation("Processing {Count} images...", imagePaths.Count);

        foreach (var imagePath in imagePaths)
        {
            try
            {
                logger.LogInformation("Analyzing image: {ImagePath}", imagePath);

                var result = await detectionService.DetectProductAsync(imagePath);

                Console.WriteLine();
                Console.WriteLine($"Image: {Path.GetFileName(imagePath)}");
                Console.WriteLine($"Product: {result.ProductName}");
                Console.WriteLine($"Confidence: {result.Confidence:F4} ({result.Confidence * 100:F2}%)");
                Console.WriteLine($"Full Path: {imagePath}");
                Console.WriteLine(new string('-', 50));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process image: {ImagePath}", imagePath);
                Console.WriteLine($"Error processing {imagePath}: {ex.Message}");
            }
        }
    }

    static async Task RunInteractiveMode(ProductDetectionService detectionService, ILogger logger)
    {
        logger.LogInformation("Starting interactive mode. Type 'exit' to quit.");

        // Show available labels
        var labels = detectionService.GetAvailableLabels();
        Console.WriteLine();
        Console.WriteLine($"Available product categories: {string.Join(", ", labels)}");
        Console.WriteLine();

        while (true)
        {
            Console.Write("Enter image path (or 'exit' to quit): ");
            var input = Console.ReadLine()?.Trim();

            if (string.IsNullOrEmpty(input) || input.ToLower() == "exit")
            {
                break;
            }

            if (!File.Exists(input))
            {
                Console.WriteLine($"File not found: {input}");
                continue;
            }

            try
            {
                Console.WriteLine("Analyzing...");
                var result = await detectionService.DetectProductAsync(input);

                Console.WriteLine();
                Console.WriteLine($"Result for: {Path.GetFileName(input)}");
                Console.WriteLine($"Product: {result.ProductName}");
                Console.WriteLine($"Confidence: {result.Confidence:F4} ({result.Confidence * 100:F2}%)");

                if (result.BoundingBox != null)
                {
                    Console.WriteLine($"Bounding Box: X={result.BoundingBox.X:F2}, Y={result.BoundingBox.Y:F2}, " +
                                    $"W={result.BoundingBox.Width:F2}, H={result.BoundingBox.Height:F2}");
                }

                Console.WriteLine(new string('-', 50));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process image: {ImagePath}", input);
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        Console.WriteLine("Goodbye!");
    }
}
