{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"ProductDetection.Core/1.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7", "Microsoft.ML": "4.0.2", "Microsoft.ML.ImageAnalytics": "4.0.2", "Microsoft.ML.Vision": "4.0.2", "SixLabors.ImageSharp": "3.1.10"}, "runtime": {"ProductDetection.Core.dll": {}}}, "Google.Protobuf/3.27.1": {"runtime": {"lib/net5.0/Google.Protobuf.dll": {"assemblyVersion": "3.27.1.0", "fileVersion": "3.27.1.0"}}}, "Microsoft.Extensions.Configuration/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.DependencyInjection/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.7": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Logging/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Options/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Primitives/9.0.7": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.ML/4.0.2": {"dependencies": {"Microsoft.ML.CpuMath": "4.0.2", "Microsoft.ML.DataView": "4.0.2", "Newtonsoft.Json": "13.0.3", "System.CodeDom": "8.0.0", "System.Collections.Immutable": "8.0.0", "System.Memory": "4.6.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Threading.Channels": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.ML.Core.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.225.12505"}, "lib/netstandard2.0/Microsoft.ML.Data.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.225.12505"}, "lib/netstandard2.0/Microsoft.ML.KMeansClustering.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.225.12505"}, "lib/netstandard2.0/Microsoft.ML.PCA.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.225.12505"}, "lib/netstandard2.0/Microsoft.ML.StandardTrainers.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.225.12505"}, "lib/netstandard2.0/Microsoft.ML.Transforms.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.225.12505"}}, "runtimeTargets": {"runtimes/linux-arm/native/libLdaNative.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libLdaNative.so.dbg": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libLdaNative.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libLdaNative.so.dbg": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libLdaNative.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libLdaNative.so.dbg": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libLdaNative.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libLdaNative.dylib.dwarf": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libLdaNative.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libLdaNative.dylib.dwarf": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/LdaNative.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.2200.225.12505"}, "runtimes/win-x64/native/LdaNative.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.2200.225.12505"}, "runtimes/win-x86/native/LdaNative.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.2200.225.12505"}}}, "Microsoft.ML.CpuMath/4.0.2": {"dependencies": {"System.Numerics.Tensors": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.ML.CpuMath.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.225.12505"}}}, "Microsoft.ML.DataView/4.0.2": {"dependencies": {"System.Collections.Immutable": "8.0.0", "System.Memory": "4.6.0"}, "runtime": {"lib/netstandard2.0/Microsoft.ML.DataView.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.225.12505"}}}, "Microsoft.ML.ImageAnalytics/4.0.2": {"dependencies": {"Microsoft.ML": "4.0.2", "SkiaSharp": "2.88.8", "SkiaSharp.NativeAssets.Linux.NoDependencies": "2.88.8", "System.Buffers": "4.6.0", "System.Runtime.CompilerServices.Unsafe": "6.1.0"}, "runtime": {"lib/netstandard2.0/Microsoft.ML.ImageAnalytics.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.225.12505"}}}, "Microsoft.ML.TensorFlow/4.0.2": {"dependencies": {"Google.Protobuf": "3.27.1", "Microsoft.ML": "4.0.2", "System.IO.FileSystem.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "TensorFlow.NET": "0.20.1"}, "runtime": {"lib/netstandard2.0/Microsoft.ML.TensorFlow.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.225.12505"}}}, "Microsoft.ML.Vision/4.0.2": {"dependencies": {"Microsoft.ML.TensorFlow": "4.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.ML.Vision.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.225.12505"}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "NumSharp.Lite/0.1.8": {"dependencies": {"System.Memory": "4.6.0"}, "runtime": {"lib/netstandard2.0/NumSharp.Lite.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Protobuf.Text/0.4.0": {"dependencies": {"Google.Protobuf": "3.27.1"}, "runtime": {"lib/netstandard2.0/Protobuf.Text.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SixLabors.ImageSharp/3.1.10": {"runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.1.10.0"}}}, "SkiaSharp/2.88.8": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.8", "SkiaSharp.NativeAssets.macOS": "2.88.8"}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"assemblyVersion": "2.88.0.0", "fileVersion": "2.88.8.0"}}}, "SkiaSharp.NativeAssets.Linux.NoDependencies/2.88.8": {"dependencies": {"SkiaSharp": "2.88.8"}, "runtimeTargets": {"runtimes/linux-arm/native/libSkiaSharp.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libSkiaSharp.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libSkiaSharp.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libSkiaSharp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.macOS/2.88.8": {"runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.Win32/2.88.8": {"runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "System.Buffers/4.6.0": {}, "System.CodeDom/8.0.0": {"runtime": {"lib/net8.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Collections.Immutable/8.0.0": {}, "System.IO.FileSystem.AccessControl/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Memory/4.6.0": {}, "System.Numerics.Tensors/8.0.0": {"runtime": {"lib/net8.0/System.Numerics.Tensors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Reflection.Emit.Lightweight/4.7.0": {}, "System.Runtime.CompilerServices.Unsafe/6.1.0": {}, "System.Security.AccessControl/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Threading.Channels/8.0.0": {}, "TensorFlow.NET/0.20.1": {"dependencies": {"Google.Protobuf": "3.27.1", "NumSharp.Lite": "0.1.8", "Protobuf.Text": "0.4.0"}, "runtime": {"lib/netstandard2.0/TensorFlow.NET.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}}}, "libraries": {"ProductDetection.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Google.Protobuf/3.27.1": {"type": "package", "serviceable": true, "sha512": "sha512-7IVz9TzhYCZ8qY0rPhXUnyJSXYdshUqmmxmTI763XmDDSJJFnyfKH43FFcMJu/CZgBcE98xlFztrKwhzcRkiPg==", "path": "google.protobuf/3.27.1", "hashPath": "google.protobuf.3.27.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-oxGR51+w5cXm5B9gU6XwpAB2sTiyPSmZm7hjvv0rzRnmL5o/KZzE103AuQj7sK26OBupjVzU/bZxDWvvU4nhEg==", "path": "microsoft.extensions.configuration/9.0.7", "hashPath": "microsoft.extensions.configuration.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-lut/kiVvNsQ120VERMUYSFhpXPpKjjql+giy03LesASPBBcC0o6+aoFdzJH9GaYpFTQ3fGVhVjKjvJDoAW5/IQ==", "path": "microsoft.extensions.configuration.abstractions/9.0.7", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-ExY+zXHhU4o9KC2alp3ZdLWyVWVRSn5INqax5ABk+HEOHlAHzomhJ7ek9HHliyOMiVGoYWYaMFOGr9q59mSAGA==", "path": "microsoft.extensions.configuration.binder/9.0.7", "hashPath": "microsoft.extensions.configuration.binder.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-i05AYA91vgq0as84ROVCyltD2gnxaba/f1Qw2rG7mUsS0gv8cPTr1Gm7jPQHq7JTr4MJoQUcanLVs16tIOUJaQ==", "path": "microsoft.extensions.dependencyinjection/9.0.7", "hashPath": "microsoft.extensions.dependencyinjection.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-iPK1FxbGFr2Xb+4Y+dTYI8Gupu9pOi8I3JPuPsrogUmEhe2hzZ9LpCmolMEBhVDo2ikcSr7G5zYiwaapHSQTew==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.7", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-fdIeQpXYV8yxSWG03cCbU2Otdrq4NWuhnQLXokWLv3L9YcK055E7u8WFJvP+uuP4CFeCEoqZQL4yPcjuXhCZrg==", "path": "microsoft.extensions.logging/9.0.7", "hashPath": "microsoft.extensions.logging.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-sMM6NEAdUTE/elJ2wqjOi0iBWqZmSyaTByLF9e8XHv6DRJFFnOe0N+s8Uc6C91E4SboQCfLswaBIZ+9ZXA98AA==", "path": "microsoft.extensions.logging.abstractions/9.0.7", "hashPath": "microsoft.extensions.logging.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-trJnF6cRWgR5uMmHpGoHmM1wOVFdIYlELlkO9zX+RfieK0321Y55zrcs4AaEymKup7dxgEN/uJU25CAcMNQRXw==", "path": "microsoft.extensions.options/9.0.7", "hashPath": "microsoft.extensions.options.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-ti/zD9BuuO50IqlvhWQs9GHxkCmoph5BHjGiWKdg2t6Or8XoyAfRJiKag+uvd/fpASnNklfsB01WpZ4fhAe0VQ==", "path": "microsoft.extensions.primitives/9.0.7", "hashPath": "microsoft.extensions.primitives.9.0.7.nupkg.sha512"}, "Microsoft.ML/4.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-qY2VXqDTPB2QdVf344dT1XBisIQOIKmaRxIvgu1YUA9XRxv5mvnwuOxxlCJEBO106wIf9oFk8xsllMnlzbpRlQ==", "path": "microsoft.ml/4.0.2", "hashPath": "microsoft.ml.4.0.2.nupkg.sha512"}, "Microsoft.ML.CpuMath/4.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-NMbatQ0tNRnMTB4u/hpNWlx9n9lDRRa220Vx78pXloy7Keiwv130SC7AnQyXwveeg6DUOIgk/qUuH3I6MLaD1w==", "path": "microsoft.ml.cpumath/4.0.2", "hashPath": "microsoft.ml.cpumath.4.0.2.nupkg.sha512"}, "Microsoft.ML.DataView/4.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-k8q9UwoCG5WpwnvPS3s46yNU+wkMgAC5F/8OELpPbozcyJw0DZL8WnpnL/mw7E218Ec4SpYz+nxL9P87eAdzmg==", "path": "microsoft.ml.dataview/4.0.2", "hashPath": "microsoft.ml.dataview.4.0.2.nupkg.sha512"}, "Microsoft.ML.ImageAnalytics/4.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-r3MFOcAtTlugRiAMI3si/KwGusnbXUg10mEZkGe0qEJe9GHC9CL2gtYIHrgaa3DcuTLhKbRyuLOjzhFAXuaPtQ==", "path": "microsoft.ml.imageanalytics/4.0.2", "hashPath": "microsoft.ml.imageanalytics.4.0.2.nupkg.sha512"}, "Microsoft.ML.TensorFlow/4.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-q/gPtcsNyIgl6vouUJjdwdhFsljCqi1uUJm27ILMkWK417BwKWy8UBPfpFYO1YUsWyrDHlcn0a3aXpMp2LwaEQ==", "path": "microsoft.ml.tensorflow/4.0.2", "hashPath": "microsoft.ml.tensorflow.4.0.2.nupkg.sha512"}, "Microsoft.ML.Vision/4.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-VdAbyfWe3dDLVGPdOmgq6k2tihurdaFVwsm+8S9NALDqVtRMhbCJSu86J041qeQdlKjnhbJYrUxSRImaGewj1A==", "path": "microsoft.ml.vision/4.0.2", "hashPath": "microsoft.ml.vision.4.0.2.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "NumSharp.Lite/0.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-8J74SVRjrsid+26pCkRo2A1e0tWIv1R/Qvin7zDVD3Pm1C3lK0XEQXNJ7nk3Qb1jwfNYCRaXBvFJZrj0X7ocTA==", "path": "numsharp.lite/0.1.8", "hashPath": "numsharp.lite.0.1.8.nupkg.sha512"}, "Protobuf.Text/0.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-fg7139wyR33UUbqJjRR8LppXORUv6FyrX1ngazHyadVmjnRi0UhwReHKbpi5/54h+PcGyue/DoV3k3IKXKhO/A==", "path": "protobuf.text/0.4.0", "hashPath": "protobuf.text.0.4.0.nupkg.sha512"}, "SixLabors.ImageSharp/3.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-R1HEPcqx3v+kvlOTPouP0g/Nzzud9pHtjlgGbFax3Ivaz8kkaGfS2EPfyDGpmfoTUQ3nQ5wxdhYyYa9fwYA9cw==", "path": "sixlabors.imagesharp/3.1.10", "hashPath": "sixlabors.imagesharp.3.1.10.nupkg.sha512"}, "SkiaSharp/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-bRkp3uKp5ZI8gXYQT57uKwil1uobb2p8c69n7v5evlB/2JNcMAXVcw9DZAP5Ig3WSvgzGm2YSn27UVeOi05NlA==", "path": "skiasharp/2.88.8", "hashPath": "skiasharp.2.88.8.nupkg.sha512"}, "SkiaSharp.NativeAssets.Linux.NoDependencies/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-/DoKtdyvRgCC5GR/SH+ps3ZiOjmf0BYpAyrhWQELFOO1hdcqddrDVJjDNCOJ41vV+NlS5b3kcDoZZ7jLhFjyXg==", "path": "skiasharp.nativeassets.linux.nodependencies/2.88.8", "hashPath": "skiasharp.nativeassets.linux.nodependencies.2.88.8.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-6Kn5TSkKlfyS6azWHF3Jk2sW5C4jCE5uSshM/5AbfFrR+5n6qM5XEnz9h4VaVl7LTxBvHvMkuPb/3bpbq0vxTw==", "path": "skiasharp.nativeassets.macos/2.88.8", "hashPath": "skiasharp.nativeassets.macos.2.88.8.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-O9QXoWEXA+6cweR4h3BOnwMz+pO9vL9mXdjLrpDd0w1QzCgWmLQBxa1VgySDITiH7nQndrDG1h6937zm9pLj1Q==", "path": "skiasharp.nativeassets.win32/2.88.8", "hashPath": "skiasharp.nativeassets.win32.2.88.8.nupkg.sha512"}, "System.Buffers/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-lN6tZi7Q46zFzAbRYXTIvfXcyvQQgxnY7Xm6C6xQ9784dEL1amjM6S6Iw4ZpsvesAKnRVsM4scrDQaDqSClkjA==", "path": "system.buffers/4.6.0", "hashPath": "system.buffers.4.6.0.nupkg.sha512"}, "System.CodeDom/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WTlRjL6KWIMr/pAaq3rYqh0TJlzpouaQ/W1eelssHgtlwHAH25jXTkUphTYx9HaIIf7XA6qs/0+YhtLEQRkJ+Q==", "path": "system.codedom/8.0.0", "hashPath": "system.codedom.8.0.0.nupkg.sha512"}, "System.Collections.Immutable/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "path": "system.collections.immutable/8.0.0", "hashPath": "system.collections.immutable.8.0.0.nupkg.sha512"}, "System.IO.FileSystem.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SxHB3nuNrpptVk+vZ/F+7OHEpoHUIKKMl02bUmYHQr1r+glbZQxs7pRtsf4ENO29TVm2TH3AEeep2fJcy92oYw==", "path": "system.io.filesystem.accesscontrol/5.0.0", "hashPath": "system.io.filesystem.accesscontrol.5.0.0.nupkg.sha512"}, "System.Memory/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-OEkbBQoklHngJ8UD8ez2AERSk2g+/qpAaSWWCBFbpH727HxDq5ydVkuncBaKcKfwRqXGWx64dS6G1SUScMsitg==", "path": "system.memory/4.6.0", "hashPath": "system.memory.4.6.0.nupkg.sha512"}, "System.Numerics.Tensors/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fhODzTe9ON9IzmRfyVeA6L8yXOciMtpq1YufkRVBliggcVKZE+XDxqIn46+yF4PWR6wNPuDpXtPpuY86VcKxUA==", "path": "system.numerics.tensors/8.0.0", "hashPath": "system.numerics.tensors.8.0.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-a4OLB4IITxAXJeV74MDx49Oq2+PsF6Sml54XAFv+2RyWwtDBcabzoxiiJRhdhx+gaohLh4hEGCLQyBozXoQPqA==", "path": "system.reflection.emit.lightweight/4.7.0", "hashPath": "system.reflection.emit.lightweight.4.7.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-5o/HZxx6RVqYlhKSq8/zronDkALJZUT2Vz0hx43f0gwe8mwlM0y2nYlqdBwLMzr262Bwvpikeb/yEwkAa5PADg==", "path": "system.runtime.compilerservices.unsafe/6.1.0", "hashPath": "system.runtime.compilerservices.unsafe.6.1.0.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Threading.Channels/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CMaFr7v+57RW7uZfZkPExsPB6ljwzhjACWW1gfU35Y56rk72B/Wu+sTqxVmGSk4SFUlPc3cjeKND0zktziyjBA==", "path": "system.threading.channels/8.0.0", "hashPath": "system.threading.channels.8.0.0.nupkg.sha512"}, "TensorFlow.NET/0.20.1": {"type": "package", "serviceable": true, "sha512": "sha512-8svOVEjS1CV5JDnUUuFKwvbM2q8KSaqUR7rFo3Ht/duACtY58lZS4Q6hwu2GeWm8gRvhJIrNngIrDbsWMbDvyg==", "path": "tensorflow.net/0.20.1", "hashPath": "tensorflow.net.0.20.1.nupkg.sha512"}}}