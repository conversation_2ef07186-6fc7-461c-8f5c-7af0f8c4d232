{"format": 1, "restore": {"E:\\rnd\\yolo2\\ProductDetection.Core\\ProductDetection.Core.csproj": {}}, "projects": {"E:\\rnd\\yolo2\\ProductDetection.Core\\ProductDetection.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\rnd\\yolo2\\ProductDetection.Core\\ProductDetection.Core.csproj", "projectName": "ProductDetection.Core", "projectPath": "E:\\rnd\\yolo2\\ProductDetection.Core\\ProductDetection.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\rnd\\yolo2\\ProductDetection.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.ML": {"target": "Package", "version": "[4.0.2, )"}, "Microsoft.ML.ImageAnalytics": {"target": "Package", "version": "[4.0.2, )"}, "Microsoft.ML.Vision": {"target": "Package", "version": "[4.0.2, )"}, "SixLabors.ImageSharp": {"target": "Package", "version": "[3.1.10, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}