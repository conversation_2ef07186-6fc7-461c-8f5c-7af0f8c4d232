using Microsoft.ML.Data;

namespace ProductDetection.Core.Models;

/// <summary>
/// Input model for product classification
/// </summary>
public class ProductClassificationInput
{
    public MLImage? Image { get; set; }
}

/// <summary>
/// Output model for product classification
/// </summary>
public class ProductClassificationOutput
{
    [ColumnName("PredictedLabel")]
    public string? PredictedLabel { get; set; }

    [ColumnName("Score")]
    public float[]? Score { get; set; }
}

/// <summary>
/// Training data for product classification
/// </summary>
public class ProductTrainingData
{
    [LoadColumn(0)]
    public string? ImagePath { get; set; }

    [LoadColumn(1)]
    public string? Label { get; set; }
}
