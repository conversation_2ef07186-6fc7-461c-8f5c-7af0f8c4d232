# Product Detection with ML.NET and YOLO

A .NET Core 9 console application that uses ML.NET for training and detecting products in images. This solution provides a secure, compiled approach to product detection that protects your code logic while delivering high-performance inference.

## Features

- **Secure Code Protection**: Compiled .NET application protects your logic from being viewed
- **ML.NET Integration**: Uses Microsoft's ML.NET framework for machine learning
- **YOLO-based Detection**: Implements object detection for product identification
- **Folder-based Training**: Automatically organizes training data by product categories
- **Easy-to-use Console Apps**: Separate applications for training and testing
- **Model Persistence**: Save and load trained models for reuse
- **Interactive Testing**: Test individual images or run in interactive mode

## Project Structure

```
ProductDetection/
├── ProductDetection.Core/           # Core library with ML logic
│   ├── Models/                      # Data models and schemas
│   └── Services/                    # Business logic services
├── ProductDetection.Training/       # Training console application
├── ProductDetection.Testing/        # Testing console application
├── products/                        # Training data directory
│   ├── product1/                    # Product category 1
│   │   ├── image1.jpg
│   │   ├── image2.jpg
│   │   └── ...
│   ├── product2/                    # Product category 2
│   │   ├── image1.jpg
│   │   └── ...
│   └── ...
└── models/                          # Saved trained models
    ├── product-detection-model.zip
    └── product-detection-model.metadata.json
```

## Prerequisites

- .NET 9.0 SDK
- Windows, macOS, or Linux
- At least 4GB RAM (8GB recommended for training)
- GPU support optional but recommended for faster training

## Setup Instructions

### 1. Clone and Build

```bash
git clone <repository-url>
cd ProductDetection
dotnet restore
dotnet build
```

### 2. Prepare Training Data

Create your product dataset with the following structure:

```
products/
├── apple/
│   ├── apple1.jpg
│   ├── apple2.jpg
│   └── apple3.jpg
├── banana/
│   ├── banana1.jpg
│   ├── banana2.jpg
│   └── banana3.jpg
└── orange/
    ├── orange1.jpg
    ├── orange2.jpg
    └── orange3.jpg
```

**Requirements:**
- Each product category should have its own folder
- Folder names will be used as product labels
- Supported image formats: .jpg, .jpeg, .png, .bmp, .gif
- Minimum 10 images per category recommended
- Images will be automatically resized to 224x224 pixels

### 3. Train Your Model

```bash
# Basic training
dotnet run --project ProductDetection.Training

# Custom training with parameters
dotnet run --project ProductDetection.Training -- \
  --products-path ./products \
  --models-path ./models \
  --model-name my-product-model \
  --epochs 50 \
  --batch-size 16 \
  --learning-rate 0.001 \
  --validation-split 0.2
```

**Training Parameters:**
- `--products-path`: Path to products directory (default: products)
- `--models-path`: Path to save models (default: models)
- `--model-name`: Name for the trained model (default: product-detection-model)
- `--epochs`: Number of training epochs (default: 100)
- `--batch-size`: Training batch size (default: 32)
- `--learning-rate`: Learning rate (default: 0.001)
- `--validation-split`: Validation data ratio (default: 0.2)

### 4. Test Your Model

```bash
# Test single image
dotnet run --project ProductDetection.Testing -- \
  --model ./models/product-detection-model.zip \
  --image ./test-image.jpg

# Test multiple images
dotnet run --project ProductDetection.Testing -- \
  --model ./models/product-detection-model.zip \
  --image ./image1.jpg \
  --image ./image2.jpg

# Interactive mode
dotnet run --project ProductDetection.Testing -- \
  --model ./models/product-detection-model.zip \
  --interactive
```

## Configuration

### Training Configuration (appsettings.json)

```json
{
  "Training": {
    "ProductsPath": "products",
    "ModelsPath": "models",
    "ModelName": "product-detection-model",
    "ImageSize": 224,
    "Epochs": 100,
    "LearningRate": 0.001,
    "BatchSize": 32,
    "ValidationSplit": 0.2
  }
}
```

### Testing Configuration (appsettings.json)

```json
{
  "Testing": {
    "ModelPath": "models/product-detection-model.zip",
    "EnableDetailedLogging": true,
    "ConfidenceThreshold": 0.5
  }
}
```

## Usage Examples

### Training Example

```bash
# Train a model with custom settings
dotnet run --project ProductDetection.Training -- \
  --products-path "C:\MyProducts" \
  --model-name "grocery-detector" \
  --epochs 200 \
  --batch-size 64
```

### Testing Examples

```bash
# Single image detection
dotnet run --project ProductDetection.Testing -- \
  -m ./models/grocery-detector.zip \
  -i ./test-images/unknown-product.jpg

# Batch processing
dotnet run --project ProductDetection.Testing -- \
  -m ./models/grocery-detector.zip \
  -i ./test1.jpg -i ./test2.jpg -i ./test3.jpg

# Interactive testing
dotnet run --project ProductDetection.Testing -- \
  -m ./models/grocery-detector.zip --interactive
```

## API Usage

You can also use the core library in your own applications:

```csharp
using ProductDetection.Core.Services;
using Microsoft.Extensions.Logging;

// Setup services
var logger = LoggerFactory.Create(builder => builder.AddConsole())
    .CreateLogger<ProductDetectionService>();
var modelPersistence = new ModelPersistenceService(logger);
var detectionService = new ProductDetectionService(logger, modelPersistence);

// Load model
await detectionService.LoadModelAsync("./models/my-model.zip");

// Detect product
var result = await detectionService.DetectProductAsync("./test-image.jpg");
Console.WriteLine($"Product: {result.ProductName}, Confidence: {result.Confidence:F4}");
```

## Performance Tips

1. **Training Data Quality**: Use high-quality, diverse images for better accuracy
2. **Data Augmentation**: Include images with different lighting, angles, and backgrounds
3. **Balanced Dataset**: Ensure similar number of images per product category
4. **Hardware**: Use GPU-enabled machine for faster training
5. **Batch Size**: Adjust batch size based on available memory
6. **Epochs**: Monitor validation accuracy to avoid overfitting

## Troubleshooting

### Common Issues

1. **Out of Memory**: Reduce batch size or image resolution
2. **Low Accuracy**: Increase training data, epochs, or adjust learning rate
3. **Model Not Found**: Check file paths and ensure model was saved correctly
4. **Image Format Error**: Ensure images are in supported formats

### Logging

Both applications provide detailed logging. Check console output for:
- Training progress and metrics
- Model loading status
- Detection results and confidence scores
- Error messages and stack traces

## Security Features

- **Compiled Code**: Your training logic is compiled and protected
- **No Source Code Exposure**: Deployed applications don't expose source code
- **Model Encryption**: Models can be encrypted for additional security
- **Secure Deployment**: Can be deployed as self-contained executables

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the logs for error details
3. Create an issue with detailed information about your problem
